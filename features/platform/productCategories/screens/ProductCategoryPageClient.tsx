"use client"

import { useState } from "react"
import type { ProductCategory } from "../lib/types"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Search } from "lucide-react"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { EditItemDrawer } from "@/features/dashboard/components/EditItemDrawer"
import {
  Edit3,
  Plus,
  MoreHorizontal,
  Package,
  ExternalLink,
  Trash2,
  Eye,
} from "lucide-react"

interface ProductCategoryPageClientProps {
  initialCategory: ProductCategory
  searchParams: { [key: string]: string | string[] | undefined }
}

import { AssociatedProductsList } from "../components/AssociatedProductsList"

export const ProductCategoryPageClient = ({ initialCategory, searchParams }: ProductCategoryPageClientProps) => {
  const router = useRouter()
  const [category, setCategory] = useState<ProductCategory>(initialCategory)
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false)
  const [activeTab, setActiveTab] = useState((searchParams.tab as string) || "overview")
  const [isAddProductDialogOpen, setIsAddProductDialogOpen] = useState(false)

  const handleCategoryUpdate = () => {
    router.refresh()
  }

  // Placeholder functions - to be implemented by other AI
  const handleAddProduct = async (productId: string) => {
    // TODO: Implement add product to category
    toast.success("Product added to category")
  }


  const handleBulkAction = async (action: string, productIds: string[]) => {
    // TODO: Implement bulk actions
    toast.success(`Bulk action ${action} completed`)
  }


  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="space-y-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">{category.title}</h1>
            <Badge variant={category.isActive ? "default" : "secondary"}>
              {category.isActive ? "Active" : "Inactive"}
            </Badge>
            {category.isInternal && <Badge variant="outline">Internal</Badge>}
          </div>
          <p className="text-muted-foreground">Manage products, hierarchy, and settings for this category</p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button onClick={() => setIsEditDrawerOpen(true)} size="sm">
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Category
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <ExternalLink className="h-4 w-4 mr-2" />
                View on Store
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Tag className="h-4 w-4 mr-2" />
                Manage SEO
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Category
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>


      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">
            Products <Badge variant="secondary" className="ml-2">{category.productsCount || category.products?.length || 0}</Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Category Details</CardTitle>
                <CardDescription>Essential information about this product category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-1">
                      <Label className="text-xs text-muted-foreground uppercase tracking-wide">Category Handle</Label>
                      <div className="flex items-center gap-2">
                        <code className="flex-1 px-3 py-2 bg-muted rounded-md text-sm font-mono">{category.handle}</code>
                      </div>
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs text-muted-foreground uppercase tracking-wide">Category ID</Label>
                      <div className="flex items-center gap-2">
                        <code className="flex-1 px-3 py-2 bg-muted rounded-md text-sm font-mono truncate">{category.id}</code>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Status and Hierarchy */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs text-muted-foreground uppercase tracking-wide mb-2 block">Status</Label>
                        <div className="flex items-center gap-2">
                          <Badge variant={category.isActive ? "default" : "secondary"}>
                            {category.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {category.isInternal && <Badge variant="outline">Internal Only</Badge>}
                        </div>
                      </div>
                      
                      <div>
                        <Label className="text-xs text-muted-foreground uppercase tracking-wide mb-2 block">Products</Label>
                        <p className="text-2xl font-semibold">{category.productsCount || category.products?.length || 0}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs text-muted-foreground uppercase tracking-wide mb-2 block">Parent Category</Label>
                        {category.parentCategory ? (
                          <Link 
                            href={`/dashboard/platform/product-categories/${category.parentCategory.id}`}
                            className="inline-flex items-center gap-2 text-sm hover:underline"
                          >
                            <Package className="h-4 w-4" />
                            {category.parentCategory.title}
                          </Link>
                        ) : (
                          <p className="text-sm text-muted-foreground">No parent category</p>
                        )}
                      </div>
                      
                      <div>
                        <Label className="text-xs text-muted-foreground uppercase tracking-wide mb-2 block">Child Categories</Label>
                        <p className="text-2xl font-semibold">{category.categoryChildrenCount || category.categoryChildren?.length || 0}</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Timestamps */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Created:</span>
                      <span className="ml-2">{new Date(category.createdAt).toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'short', 
                        day: 'numeric' 
                      })}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Updated:</span>
                      <span className="ml-2">{new Date(category.updatedAt).toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'short', 
                        day: 'numeric' 
                      })}</span>
                    </div>
                  </div>

                  {/* Metadata */}
                  {category.metadata && Object.keys(category.metadata).length > 0 && (
                    <>
                      <Separator />
                      <div>
                        <Label className="text-xs text-muted-foreground uppercase tracking-wide mb-2 block">Metadata</Label>
                        <pre className="p-3 bg-muted rounded-md text-xs overflow-x-auto font-mono">
                          {JSON.stringify(category.metadata, null, 2)}
                        </pre>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" onClick={() => setIsAddProductDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Products
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={() => setIsEditDrawerOpen(true)}>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit Category
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View on Store
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <CardTitle>Products in Category</CardTitle>
                  <CardDescription>Manage products associated with this category</CardDescription>
                </div>
                <Button onClick={() => setIsAddProductDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Products
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <AssociatedProductsList categoryId={category.id} />
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>

      {/* Add Products Dialog */}
      <Dialog open={isAddProductDialogOpen} onOpenChange={setIsAddProductDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Add Products to Category</DialogTitle>
            <DialogDescription>Search and select products to add to this category</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search products by name, SKU, or handle..." className="pl-9" />
            </div>
            <div className="max-h-96 overflow-y-auto space-y-2">
              {/* Placeholder for product search results */}
              <div className="text-center py-8 text-muted-foreground">
                <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Search for products to add to this category</p>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsAddProductDialogOpen(false)}>
                Cancel
              </Button>
              <Button>Add Selected Products</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Category Drawer */}
      <EditItemDrawer
        listKey="ProductCategory"
        itemId={category.id}
        open={isEditDrawerOpen}
        onClose={() => setIsEditDrawerOpen(false)}
        onSaved={handleCategoryUpdate}
      />
    </div>
  )
}

export default ProductCategoryPageClient
