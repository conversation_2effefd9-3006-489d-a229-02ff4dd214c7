# Custom View System for Product Images

This directory contains a custom view system that allows you to override field rendering with custom components, similar to the pattern used in the old products folder.

## Components

### CustomFields.tsx
A TypeScript version of the old CustomFields.js component that supports:
- **fieldViews**: Override specific fields by their path (e.g., "images")
- **fieldTypeViews**: Override all fields of a certain type (e.g., "image")
- **Fallback**: Falls back to the default field view if no custom view is found

### views/Image/
Custom image field components:
- **Field.tsx**: The main image field component for editing
- **index.tsx**: Exports Field, Cell, CardValue, and controller for the image view

### CustomImageExample.tsx
A demonstration component showing how to use the custom view system.

## Usage

### Basic Usage with Field Path Override

```tsx
import { CustomFields } from "./CustomFields";
import * as ImageView from "./views/Image";

const customFieldViews = {
  images: ImageView, // Override the "images" field specifically
};

<CustomFields
  fields={fields}
  value={fieldValues}
  forceValidation={false}
  onChange={setFieldValues}
  fieldViews={customFieldViews}
/>
```

### Usage with Field Type Override

```tsx
const customFieldTypeViews = {
  image: ImageView, // Override all fields of type "image"
};

<CustomFields
  fields={fields}
  value={fieldValues}
  forceValidation={false}
  onChange={setFieldValues}
  fieldTypeViews={customFieldTypeViews}
/>
```

### Priority Order

1. **fieldViews** (specific field path) - highest priority
2. **fieldTypeViews** (field type) - medium priority  
3. **Default field view** - fallback

## Key Features

- **TypeScript Support**: Full type safety with proper interfaces
- **Backward Compatibility**: Maintains the same API as the original Fields component
- **Flexible Overrides**: Can override by field path or field type
- **Incremental Adoption**: Can be used alongside existing field components

## Next Steps

1. **Integration**: Integrate the CustomFields component into the product images media tab
2. **Enhanced Views**: Add more sophisticated image management features
3. **Testing**: Write tests to ensure the custom view system works correctly
4. **Documentation**: Add more detailed documentation and examples

## File Structure

```
features/platform/products/components/
├── CustomFields.tsx              # Main custom fields component
├── CustomImageExample.tsx        # Example usage component
├── views/
│   └── Image/
│       ├── Field.tsx             # Image field component
│       └── index.tsx             # Image view exports
└── README.md                     # This file
```

This system provides a foundation for creating custom field views while maintaining compatibility with the existing Keystone field system.
