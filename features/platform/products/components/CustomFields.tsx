"use client";

import { memo, useId, useMemo, ReactNode } from "react";
import { buttonVariants } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils/cn";
import type { FieldMeta as AdminFieldMeta } from '@/features/dashboard/types';
import type { Value } from '@/features/dashboard/lib/useChangedFieldsAndDataForUpdate';

// Field description component
const FieldDescription = ({
  children,
  id,
  className
}: {
  children: React.ReactNode;
  id: string;
  className?: string
}) => {
  return (
    <p id={id} className={className}>
      {children}
    </p>
  );
};

export interface FieldGroupMeta {
  label: string;
  description?: string | null;
  fields: { path: string }[];
  collapsed?: boolean;
}

// We use the Value type directly from the dashboard

// Custom view interfaces
interface CustomFieldView {
  Field: React.ComponentType<any>;
}

interface CustomFieldTypeViews {
  [fieldType: string]: CustomFieldView;
}

interface CustomFieldViews {
  [fieldPath: string]: CustomFieldView;
}

const RenderField = memo(function RenderField({
  field,
  value,
  autoFocus,
  forceValidation,
  onChange,
  fieldTypeViews = {},
  fieldViews = {}
}: {
  field: AdminFieldMeta;
  value: unknown;
  autoFocus?: boolean;
  forceValidation?: boolean;
  onChange?: (value: unknown) => void;
  fieldTypeViews?: CustomFieldTypeViews;
  fieldViews?: CustomFieldViews;
}) {
  const memoizedOnChange = useMemo(() => {
    if (onChange === undefined) return undefined;
    return (value: unknown) => {
      onChange(value);
    };
  }, [onChange]);

  // First check for a specific field override by path
  if (fieldViews[field.path]) {
    const CustomField = fieldViews[field.path].Field;
    return (
      <CustomField
        field={field.controller}
        value={value}
        onChange={memoizedOnChange}
        autoFocus={autoFocus}
        forceValidation={forceValidation}
      />
    );
  }

  // Then check for a field type override
  // Note: fieldType would need to be added to the controller interface
  const fieldType = (field.controller as any).fieldType;
  if (fieldType && fieldTypeViews[fieldType]) {
    const CustomField = fieldTypeViews[fieldType].Field;
    return (
      <CustomField
        field={field.controller}
        value={value}
        onChange={memoizedOnChange}
        autoFocus={autoFocus}
        forceValidation={forceValidation}
        itemValue={{}} // Add required itemValue prop
      />
    );
  }

  // Finally fall back to the default field view
  const FieldComponent = field.views?.Field;
  if (!FieldComponent) return null;

  return (
    <FieldComponent
      field={{
        ...field.controller,
        hideButtons: typeof field.fieldMeta === 'object' && field.fieldMeta !== null && 'hideButtons' in field.fieldMeta ? field.fieldMeta.hideButtons : undefined,
      }}
      onChange={memoizedOnChange}
      value={value}
      autoFocus={autoFocus}
      forceValidation={forceValidation}
      itemValue={{}} // Add required itemValue prop
    />
  );
});

export function CustomFields({
  fields,
  value,
  fieldModes = null,
  fieldPositions = null,
  forceValidation,
  invalidFields,
  position = "form",
  groups = [],
  onChange,
  fieldTypeViews = {},
  fieldViews = {}
}: {
  fields: Record<string, AdminFieldMeta>;
  value: Value;
  fieldModes?: Record<string, "edit" | "read" | "hidden"> | null;
  fieldPositions?: Record<string, "form" | "sidebar"> | null;
  forceValidation: boolean;
  invalidFields?: ReadonlySet<string>;
  position?: "form" | "sidebar";
  groups?: FieldGroupMeta[];
  onChange?: (valueUpdater: (prevValue: Value) => Value) => void;
  fieldTypeViews?: CustomFieldTypeViews;
  fieldViews?: CustomFieldViews;
}) {
  const renderedFields = Object.fromEntries(
    Object.keys(fields).map((fieldKey) => {
      const field = fields[fieldKey];
      const val = value?.[fieldKey];
      const fieldMode = fieldModes === null ? "edit" : fieldModes[fieldKey];
      const fieldPosition =
        fieldPositions === null ? "form" : fieldPositions[fieldKey];

      // Skip if field should be hidden or not in this position
      if (fieldMode === "hidden") return [fieldKey, null];
      if (fieldPosition !== position) return [fieldKey, null];

      // Handle error state
      if (val?.kind === "error" && val.errors?.[0]) {
        return [
          fieldKey,
          <div key={fieldKey}>
            {field.label}:{" "}
            <span className="text-red-600 dark:text-red-700 text-sm">
              {val.errors[0].message}
            </span>
          </div>,
        ];
      }

      // Skip if no value is available
      if (!val) return [fieldKey, null];

      return [
        fieldKey,
        <RenderField
          key={fieldKey}
          field={field}
          value={val.kind === "value" ? val.value : undefined}
          forceValidation={forceValidation && invalidFields?.has(fieldKey)}
          onChange={fieldMode === "edit" && onChange !== undefined ?
            (newFieldValue: unknown) => {
              onChange((prevValue: Value) => ({
                ...prevValue,
                [field.controller.path]: { kind: "value" as const, value: newFieldValue },
              }));
            } : undefined
          }
          fieldTypeViews={fieldTypeViews}
          fieldViews={fieldViews}
        />,
      ];
    })
  );

  const rendered: ReactNode[] = [];
  const fieldGroups = new Map();
  for (const group of groups) {
    const state = { group, rendered: false };
    for (const field of group.fields) {
      fieldGroups.set(field.path, state);
    }
  }

  for (const field of Object.values(fields)) {
    const fieldKey = field.path;
    if (fieldGroups.has(fieldKey)) {
      const groupState = fieldGroups.get(field.path);
      if (groupState.rendered) {
        continue;
      }
      groupState.rendered = true;
      const { group } = groupState;
      const renderedFieldsInGroup = group.fields.map(
        (field: { path: string }) => renderedFields[field.path]
      );
      if (renderedFieldsInGroup.every((field: ReactNode | null) => field === null)) {
        continue;
      }

      rendered.push(
        <FieldGroup
          key={group.label}
          count={group.fields.length}
          label={group.label}
          description={group.description}
          collapsed={group.collapsed}
        >
          {renderedFieldsInGroup}
        </FieldGroup>
      );
      continue;
    }
    if (renderedFields[fieldKey] === null) {
      continue;
    }
    rendered.push(renderedFields[fieldKey]);
  }

  return (
    <div className="grid w-full items-center gap-4">
      {rendered.length === 0 && value && Object.keys(value).length === 0
        ? "There are no fields that you can read or edit"
        : rendered}
    </div>
  );
}

function FieldGroup(props: {
  label: string;
  description?: string | null;
  count: number;
  collapsed?: boolean;
  children: ReactNode;
}) {
  const descriptionId = useId();
  const labelId = useId();

  const divider = <Separator orientation="vertical" />;

  // Count actual fields (excluding virtual fields in create view)
  const actualFieldCount = props.children ? Array.isArray(props.children) ? props.children.filter(
    (item) =>
      item !== undefined &&
      !(
        item?.props?.value &&
        typeof item?.props?.value === "symbol" &&
        item?.props?.value.toString() ===
          "Symbol(create view virtual field value)"
      )
  ).length : 1 : 0;

  // Don't render the group if there are no actual fields
  if (actualFieldCount === 0) {
    return null;
  }

  return (
    <div
      role="group"
      aria-labelledby={labelId}
      aria-describedby={props.description === null || props.description === undefined ? undefined : descriptionId}
    >
      <details open={!props.collapsed} className="group">
        <summary className="list-none outline-none [&::-webkit-details-marker]:hidden cursor-pointer">
          <div className="flex gap-1.5">
            <span>
              <div
                className={cn(
                  buttonVariants({ variant: "outline", size: "icon" }),
                  "border self-start transition-transform group-open:rotate-90 [&_svg]:size-3 h-6 w-6"
                )}
              >
                <ChevronRight />
              </div>
            </span>
            {divider}
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-2">
                <div id={labelId} className="relative text-lg/5 font-medium">
                  {props.label}
                </div>
                <Badge className="text-[.7rem] py-0.5 uppercase tracking-wide font-medium">
                  {actualFieldCount} FIELD{actualFieldCount !== 1 && "S"}
                </Badge>
              </div>
              {props.description !== null && props.description !== undefined && (
                <FieldDescription
                  className="opacity-50 text-sm"
                  id={descriptionId}
                >
                  {props.description}
                </FieldDescription>
              )}
            </div>
          </div>
        </summary>
        <div className="flex ml-[2.25rem] mt-2">
          {divider}
          <div className="w-full">
            <div className="space-y-4">{props.children}</div>
          </div>
        </div>
      </details>
    </div>
  );
}
