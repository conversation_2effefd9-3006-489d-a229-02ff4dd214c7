"use client";

import { CustomFields } from "./CustomFields";
import * as ImageView from "../views/image";
import * as RelationshipView from "../views/relationship";
import type { FieldMeta } from "@/features/dashboard/types";
import type { Value } from "@/features/dashboard/lib/useChangedFieldsAndDataForUpdate";

interface MediaTabProps {
  fields: Record<string, FieldMeta>;
  value: Value;
  onChange: (valueUpdater: (prevValue: Value) => Value) => void;
  forceValidation: boolean;
  invalidFields?: ReadonlySet<string>;
  fieldModes?: Record<string, "edit" | "read" | "hidden"> | null;
  fieldPositions?: Record<string, "form" | "sidebar"> | null;
}

export function MediaTab({
  fields,
  value,
  onChange,
  forceValidation,
  invalidFields,
  fieldModes = null,
  fieldPositions = null,
}: MediaTabProps) {
  // Define custom views for specific fields by path
  const fieldViews = {
    productImages: ImageView, // Override the productImages field with our custom Image view
    // Add other specific field overrides here as needed
  };

  // Define custom views for field types
  const fieldTypeViews = {
    image: ImageView, // Override all image type fields with our custom Image view
    relationship: RelationshipView, // Override all relationship type fields with our custom Relationship view
  };

  return (
    <CustomFields
      fields={fields}
      value={value}
      onChange={onChange}
      forceValidation={forceValidation}
      invalidFields={invalidFields}
      fieldModes={fieldModes}
      fieldPositions={fieldPositions}
      fieldViews={fieldViews}
      fieldTypeViews={fieldTypeViews}
    />
  );
}
