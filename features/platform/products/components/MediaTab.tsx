"use client";

import { CustomFields } from "./CustomFields";
import * as ImageView from "./views/Image";
import type { FieldMeta } from "@/features/dashboard/types";

interface FieldValue {
  kind: 'error' | 'value';
  errors?: Array<{ message: string }>;
  value?: unknown;
}

interface MediaTabProps {
  fields: Record<string, FieldMeta>;
  value: Record<string, FieldValue>;
  onChange: (value: Record<string, FieldValue>) => void;
  forceValidation: boolean;
  invalidFields?: ReadonlySet<string>;
  fieldModes?: Record<string, "edit" | "read" | "hidden"> | null;
  fieldPositions?: Record<string, "form" | "sidebar"> | null;
}

export function MediaTab({
  fields,
  value,
  onChange,
  forceValidation,
  invalidFields,
  fieldModes = null,
  fieldPositions = null,
}: MediaTabProps) {
  // Define custom views for specific fields by path
  const fieldViews = {
    productImages: ImageView, // Override the productImages field with our custom Image view
  };

  // Define custom views for field types
  const fieldTypeViews = {
    image: ImageView, // Override all image type fields with our custom Image view
  };

  return (
    <CustomFields
      fields={fields}
      value={value}
      onChange={onChange}
      forceValidation={forceValidation}
      invalidFields={invalidFields}
      fieldModes={fieldModes}
      fieldPositions={fieldPositions}
      fieldViews={fieldViews}
      fieldTypeViews={fieldTypeViews}
    />
  );
}
