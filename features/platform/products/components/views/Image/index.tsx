"use client";

import { useState } from "react";
import Image from "next/image";
import { Pencil, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// Export the Field component
export { Field } from "./Field";

// Simple field container components
const FieldContainer = ({ children }: { children: React.ReactNode }) => {
  return <div className="space-y-2">{children}</div>;
};

const FieldLabel = ({ children }: { children: React.ReactNode }) => {
  return <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">{children}</label>;
};

// Types
interface ImageData {
  url?: string;
  filename?: string;
  id?: string;
  extension?: string;
  width?: number;
  height?: number;
  filesize?: number;
}

interface ImageValue {
  kind: 'empty' | 'from-server' | 'upload' | 'remove';
  data?: ImageData;
  previous?: ImageValue;
}

interface FieldController {
  path: string;
  label: string;
  description?: string | null;
}

interface CellProps {
  item: Record<string, any>;
  field: FieldController;
}

interface CardValueProps {
  item: Record<string, any>;
  field: FieldController;
  value?: ImageValue;
  onChange?: (value: ImageValue) => void;
}

// Cell component for list view
export const Cell = ({ item, field }: CellProps) => {
  const data = item[field.path];
  if (!data) return null;
  return (
    <div className="flex items-center h-6 w-6 leading-none">
      <img
        alt={data.filename || 'Image'}
        className="max-h-full max-w-full"
        src={data.url}
      />
    </div>
  );
};

// CardValue component for detailed view - simplified placeholder for now
export const CardValue = ({ item, field, value, onChange }: CardValueProps) => {
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const data = item[field.path];

  if (!data) {
    return (
      <FieldContainer>
        <FieldLabel>{field.label}</FieldLabel>
        <div className="aspect-square overflow-hidden rounded-md border bg-muted p-8 text-center text-sm text-muted-foreground">
          No image uploaded
        </div>
      </FieldContainer>
    );
  }

  return (
    <FieldContainer>
      <FieldLabel>{field.label}</FieldLabel>
      <div className="group relative aspect-square overflow-hidden rounded-md border bg-muted">
        <Image
          src={data.url || "/placeholder.svg"}
          alt={data.filename || 'Image'}
          className="object-cover"
          fill
          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
        />
        <div className="absolute inset-0 flex items-center justify-center gap-2 bg-black/60 opacity-0 transition-opacity group-hover:opacity-100">
          <Button
            type="button"
            variant="secondary"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedImage(data);
              setEditDialogOpen(true);
            }}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant="destructive"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              // Handle remove image - placeholder for now
              console.log('Remove image clicked');
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-2">
          <p className="truncate text-xs text-white">{data.filename || 'Untitled'}</p>
        </div>
      </div>

      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Image Details</DialogTitle>
            <DialogDescription>Update the image details.</DialogDescription>
          </DialogHeader>
          {selectedImage && (
            <div className="grid gap-4 py-4">
              <div className="relative mx-auto aspect-square w-full max-w-[200px] overflow-hidden rounded-md">
                <Image
                  src={selectedImage.url || "/placeholder.svg"}
                  alt={selectedImage.filename || 'Image'}
                  className="object-cover"
                  fill
                  sizes="200px"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="filename">Filename</Label>
                <Input
                  id="filename"
                  defaultValue={selectedImage.filename}
                  onChange={(e) => {
                    if (selectedImage) {
                      setSelectedImage({
                        ...selectedImage,
                        filename: e.target.value,
                      });
                    }
                  }}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button type="button" variant="secondary" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              type="button"
              onClick={() => {
                // Handle update image details - placeholder for now
                console.log('Save changes clicked', selectedImage);
                setEditDialogOpen(false);
              }}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </FieldContainer>
  );
};

// Controller function - simplified placeholder for now
export const controller = (config: any) => {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: `${config.path} {
        url
        id
        filename
        extension
        width
        height
        filesize
      }`,
    defaultValue: { kind: "empty" },
    deserialize(item: any) {
      const value = item[config.path];
      if (!value) return { kind: "empty" };
      return {
        kind: "from-server",
        data: {
          src: value.url,
          id: value.id,
          filename: value.filename,
          extension: value.extension,
          ref: value.ref,
          width: value.width,
          height: value.height,
          filesize: value.filesize,
        },
      };
    },
    validate(value: ImageValue) {
      // Simplified validation - placeholder for now
      return value.kind !== "upload" || true;
    },
    serialize(value: ImageValue) {
      if (value.kind === "upload") {
        return { [config.path]: { upload: value.data?.file } };
      }
      if (value.kind === "remove") {
        return { [config.path]: null };
      }
      return {};
    },
  };
};
