"use client";

import { useState } from "react";
import { CustomFields } from "./CustomFields";
import * as ImageView from "./views/Image";

// Example component demonstrating how to use CustomFields with custom Image view
export function CustomImageExample() {
  // Mock field data - this would come from your actual field metadata
  const mockFields = {
    images: {
      path: "images",
      label: "Product Images",
      description: "Upload and manage product images",
      fieldMeta: null,
      viewsIndex: 0,
      customViewsIndex: null,
      views: {
        Field: ImageView.Field,
        Cell: ImageView.Cell,
        CardValue: ImageView.CardValue,
      },
      controller: {
        path: "images",
        label: "Product Images",
        description: "Upload and manage product images",
        graphqlSelection: `images {
          url
          id
          filename
          extension
          width
          height
          filesize
        }`,
        defaultValue: { kind: "empty" },
        deserialize: (item: any) => {
          const value = item.images;
          if (!value) return { kind: "empty" };
          return {
            kind: "from-server",
            data: {
              src: value.url,
              id: value.id,
              filename: value.filename,
              extension: value.extension,
              width: value.width,
              height: value.height,
              filesize: value.filesize,
            },
          };
        },
        serialize: (value: any) => {
          if (value.kind === "upload") {
            return { images: { upload: value.data.file } };
          }
          if (value.kind === "remove") {
            return { images: null };
          }
          return {};
        },
        validate: (value: any) => true,
        fieldType: "image",
      },
      search: null,
      graphql: {
        isNonNull: [],
      },
      createView: {
        fieldMode: "edit" as const,
      },
      itemView: {
        fieldMode: "edit" as const,
        fieldPosition: "form" as const,
      },
      listView: {
        fieldMode: "read" as const,
      },
      isFilterable: false,
      isOrderable: false,
    },
  };

  // Mock field values
  const [fieldValues, setFieldValues] = useState({
    images: {
      kind: "empty" as const,
      value: { kind: "empty" },
    },
  });

  // Custom field views - this is where you can override specific fields
  const customFieldViews = {
    images: ImageView, // Override the images field with our custom Image view
  };

  // Custom field type views - this is where you can override all fields of a certain type
  const customFieldTypeViews = {
    image: ImageView, // Override all image type fields with our custom Image view
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Custom Image Field Example</h2>
        <p className="text-muted-foreground mb-6">
          This demonstrates the custom fields system with a custom image view.
          The CustomFields component can override specific fields or field types with custom views.
        </p>
      </div>

      <div className="border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Using Custom Field Views (by field path)</h3>
        <CustomFields
          fields={mockFields}
          value={fieldValues}
          forceValidation={false}
          onChange={setFieldValues}
          fieldViews={customFieldViews} // Override specific fields by path
        />
      </div>

      <div className="border rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Using Custom Field Type Views (by field type)</h3>
        <CustomFields
          fields={mockFields}
          value={fieldValues}
          forceValidation={false}
          onChange={setFieldValues}
          fieldTypeViews={customFieldTypeViews} // Override all fields of a certain type
        />
      </div>

      <div className="border rounded-lg p-6 bg-muted/50">
        <h3 className="text-lg font-semibold mb-4">Current Field Values</h3>
        <pre className="text-sm bg-background p-4 rounded border overflow-auto">
          {JSON.stringify(fieldValues, null, 2)}
        </pre>
      </div>

      <div className="text-sm text-muted-foreground space-y-2">
        <p><strong>Key Features:</strong></p>
        <ul className="list-disc list-inside space-y-1">
          <li><strong>fieldViews</strong>: Override specific fields by their path (e.g., "images")</li>
          <li><strong>fieldTypeViews</strong>: Override all fields of a certain type (e.g., "image")</li>
          <li><strong>Fallback</strong>: Falls back to the default field view if no custom view is found</li>
          <li><strong>TypeScript</strong>: Full TypeScript support with proper type checking</li>
        </ul>
      </div>
    </div>
  );
}
