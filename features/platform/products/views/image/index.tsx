"use client";

import { Field as ImageField } from './Field';

// Export the Field component
export { ImageField as Field };

// Re-export components from Field.tsx for convenience
export { ImageWrapper, Placeholder, validateImage, ImageMeta } from './Field';

interface CellProps {
  item: Record<string, any>;
  field: { path: string; label: string };
}

export function Cell({ item, field }: CellProps) {
  const data = item[field.path];
  if (!data) return null;
  return (
    <div className="flex items-center h-6 w-6 leading-none">
      <img
        alt={data.filename || 'Image'}
        className="max-h-full max-w-full"
        src={data.url}
      />
    </div>
  );
}

export function CardValue({ item, field }: CellProps) {
  const data = item[field.path];
  return (
    <div className="space-y-2">
      <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
        {field.label}
      </label>
      {data && (
        <div className="relative overflow-hidden flex-shrink-0 leading-none bg-muted/40 rounded-md text-center w-[120px] h-[120px] border">
          <img className="object-contain w-full h-full" alt={data.filename || 'Image'} src={data.url} />
        </div>
      )}
    </div>
  );
}

interface Config {
  path: string;
  label: string;
  description?: string;
}

export const controller = (config: Config) => {
  return {
    path: config.path,
    label: config.label,
    description: config.description,
    graphqlSelection: `${config.path} {
      url
      id
      filename
      extension
      width
      height
      filesize
    }`,
    defaultValue: { kind: "empty" },
    deserialize(item: Record<string, any>) {
      const value = item[config.path];
      if (!value) return { kind: "empty" };
      return {
        kind: "from-server",
        data: {
          src: value.url,
          id: value.id,
          filename: value.filename,
          extension: value.extension,
          ref: value.ref,
          width: value.width,
          height: value.height,
          filesize: value.filesize,
        },
      };
    },
    validate(value: any) {
      return value.kind !== "upload" || validateImage(value.data) === undefined;
    },
    serialize(value: any) {
      if (value?.kind === "upload") {
        return { [config.path]: { upload: value.data.file } };
      }
      if (value?.kind === "remove") {
        return { [config.path]: null };
      }
      return {};
    },
  };
};
