"use client";

import { Fragment, useEffect, useRef, useState } from "react";
import { gql } from "@apollo/client";
import { useQuery } from "@apollo/client";
import { makeDataGetter } from "@/features/dashboard/lib/dataGetter";
import { cn } from "@/lib/utils";
import { InlineEdit } from "./InlineEdit";
import { InlineCreate } from "./InlineCreate";
import { RelationshipSelect } from "@/features/dashboard/views/relationship/client/components/RelationshipSelect";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { FieldContainer } from "@/components/ui/field-container";
import { FieldLabel } from "@/components/ui/field-label";
import { FieldLegend } from "@/components/ui/field-legend";
import { FieldDescription } from "@/components/ui/field-description";
import { CreateItemDrawer } from "@/features/dashboard/components/CreateItemDrawer";
import Link from "next/link";
import { basePath } from "@/features/dashboard/lib/config";

const CardContainer = ({ mode = "view", ...props }: { mode?: "view" | "edit" | "create"; children: React.ReactNode; className?: string }) => {
  return (
    <div
      className={cn(
        `relative before:content-[' '] before:rounded-xl before:w-1 before:h-full before:absolute before:left-0 before:top-0 before:bottom-0 before:z-10`,
        // mode === "create" ? "before:bg-emerald-500" : "before:bg-blue-500"
        props.className
      )}
      {...props}
    />
  );
};

export function Cards({
  localList,
  field,
  foreignList,
  id,
  value,
  onChange,
  forceValidation,
}: {
  localList: any;
  field: any;
  foreignList: any;
  id: string | null;
  value: any;
  onChange?: (value: any) => void;
  forceValidation?: boolean;
}) {
  const { displayOptions } = value;
  let selectedFields = [
    ...new Set([
      ...displayOptions.cardFields,
      ...(displayOptions.inlineEdit?.fields || []),
    ]),
  ]
    .map((fieldPath: string) => {
      return foreignList.fields[fieldPath].controller.graphqlSelection;
    })
    .join("\n");
  if (!displayOptions.cardFields.includes("id")) {
    selectedFields += "\nid";
  }
  if (
    !displayOptions.cardFields.includes(foreignList.labelField) &&
    foreignList.labelField !== "id"
  ) {
    selectedFields += `\n${foreignList.labelField}`;
  }

  // Simplified implementation - use the data that's already available
  const currentIdsArray = Array.from(value.currentIds || []);

  // For now, we'll create a simple mock implementation
  // In a real implementation, you'd fetch the actual data using the IDs
  const items: Record<string, any> = {};
  currentIdsArray.forEach((id) => {
    items[id] = {
      data: { id },
      get: (fieldPath: string) => ({
        data: fieldPath === 'image' ? {
          url: `https://images.unsplash.com/photo-${Math.random() > 0.5 ? '1505740420928-5e560c06d30e' : '1542291026-7eec264c27ff'}?w=300&h=300&fit=crop`,
          filename: `image-${id}.jpg`,
          filesize: 245760,
          width: 300,
          height: 300
        } : fieldPath === 'altText' ? `Alt text for ${id}` : `Value for ${fieldPath}`,
        errors: undefined
      })
    };
  });

  const itemsState = { kind: "loaded" };

  const [isLoadingLazyItems, setIsLoadingLazyItems] = useState(false);
  const [showConnectItems, setShowConnectItems] = useState(false);
  const [hideConnectItemsLabel, setHideConnectItemsLabel] = useState("Cancel");
  const editRef = useRef(null);

  const isMountedRef = useRef(false);
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  });

  if (itemsState.kind === "loading") {
    return null;
  }
  if (itemsState.kind === "error") {
    return (
      <span className="text-red-600 dark:text-red-500 text-sm">
        {itemsState.message}
      </span>
    );
  }

  const currentIdsArrayWithFetchedItems = currentIdsArray
    .map((id: string) => ({ itemGetter: items[id], id }))
    .filter((x) => x.itemGetter);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {/* Inline Create */}
        <div>
          <CardContainer mode="create">
            <InlineCreate
              selectedFields={selectedFields}
              fields={displayOptions.inlineCreate?.fields || []}
              list={foreignList}
              onCancel={() => {
                onChange?.({ ...value, itemBeingCreated: false });
              }}
              onCreate={(itemGetter: any) => {
                const id = itemGetter.data.id;
                onChange?.({
                  ...value,
                  itemBeingCreated: false,
                  currentIds: field.many
                    ? new Set([...value.currentIds, id])
                    : new Set([id]),
                });
              }}
            />
          </CardContainer>
        </div>

        {/* Inline Edit */}
        {currentIdsArrayWithFetchedItems.map(({ id, itemGetter }, index) => (
          <div key={id}>
            <CardContainer mode="edit">
              <InlineEdit
                list={foreignList}
                fields={displayOptions.inlineEdit?.fields || []}
                onSave={(newItemGetter: any) => {
                  // Handle save
                }}
                selectedFields={selectedFields}
                itemGetter={itemGetter}
                onCancel={() => {}}
              />
            </CardContainer>
          </div>
        ))}
      </div>

      {/* Connect Items Section - Simplified */}
      {onChange && displayOptions.inlineConnect && showConnectItems && (
        <CardContainer mode="edit">
          <div className="flex gap-2 p-4">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowConnectItems(false)}
            >
              {hideConnectItemsLabel}
            </Button>
          </div>
        </CardContainer>
      )}
      {forceValidation && (
        <span className="text-red-600 dark:text-red-500 text-sm">
          You must finish creating and editing any related{" "}
          {foreignList.label.toLowerCase()} before saving the{" "}
          {localList.singular.toLowerCase()}
        </span>
      )}
    </div>
  );
}
