"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus } from "lucide-react";

export function InlineCreate({
  selectedFields,
  fields,
  list,
  onCancel,
  onCreate,
}: {
  selectedFields: string;
  fields: string[];
  list: any;
  onCancel: () => void;
  onCreate: (itemGetter: any) => void;
}) {
  const [isCreating, setIsCreating] = useState(false);

  if (isCreating) {
    return (
      <Card className="border-dashed border-2">
        <CardContent className="p-4">
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Create New {list.singular}</h3>
            
            {/* This would be the actual create form */}
            <div className="text-sm text-muted-foreground">
              Create form for {list.singular} would go here...
            </div>
            
            <div className="flex gap-2">
              <Button size="sm" onClick={() => {
                // Mock creation - in real implementation this would create the item
                const mockItemGetter = {
                  data: { id: `new-${Date.now()}` },
                  get: (field: string) => ({ data: `mock-${field}` })
                };
                onCreate(mockItemGetter);
                setIsCreating(false);
              }}>
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={() => {
                setIsCreating(false);
                onCancel();
              }}>
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-dashed border-2 hover:border-solid transition-colors cursor-pointer" onClick={() => setIsCreating(true)}>
      <CardContent className="p-8">
        <div className="flex flex-col items-center justify-center text-center space-y-2">
          <Plus className="h-8 w-8 text-muted-foreground" />
          <p className="text-sm font-medium">Add {list.singular}</p>
          <p className="text-xs text-muted-foreground">Click to create new</p>
        </div>
      </CardContent>
    </Card>
  );
}
