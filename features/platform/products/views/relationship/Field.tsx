"use client";

import { RelationshipSelect } from "@/features/dashboard/views/relationship/client/components/RelationshipSelect";
import { useList } from "@/features/dashboard/hooks/useAdminMeta";

export const ClientField = ({
  field,
  value,
  onChange,
  forceValidation,
}: {
  field: any;
  value: any;
  onChange: (value: any) => void;
  forceValidation?: boolean;
}) => {
  const { list } = useList(field.refListKey) || { list: undefined };

  if (!list) {
    return <div>Loading...</div>;
  }

  return (
    <RelationshipSelect
      controlShouldRenderValue
      list={list}
      labelField={field.refLabelField || "name"}
      searchFields={field.refSearchFields || ["name"]}
      state={value}
    />
  );
};
