"use client";

import { Fragment, useState } from "react";
import useS<PERSON> from "swr";
import { getRelationshipOptions } from "@/features/dashboard/actions";
import { RelationshipSelect } from "@/features/dashboard/views/relationship/client/components/RelationshipSelect";
import { ClientField } from "./Field";
import Link from "next/link";
import { useList } from "@/features/dashboard/hooks/useAdminMeta";
import { basePath } from "@/features/dashboard/lib/config";
import { FieldContainer } from "@/components/ui/field-container";
import { FieldLabel } from "@/components/ui/field-label";
import { FieldDescription } from "@/components/ui/field-description";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Pencil, Trash2, ExternalLink } from "lucide-react";
import Image from "next/image";

const CellContainer: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => <div>{children}</div>;

function LinkToRelatedItems({ itemId, value, list, refFieldKey }: {
  itemId?: string;
  value: any;
  list: any;
  refFieldKey?: string;
}) {
  function constructQuery({ refFieldKey, itemId, value }: {
    refFieldKey?: string;
    itemId?: string;
    value: any;
  }) {
    if (!!refFieldKey && itemId) {
      return `!${refFieldKey}_matches="${itemId}"`;
    }
    return `!id_in="${(value?.value)
      .slice(0, 100)
      .map(({ id }: any) => id)
      .join(",")}"`;
  }
  if (value.kind === "many") {
    const query = constructQuery({ refFieldKey, value, itemId });
    return (
      <Button variant="ghost">
        <Link href={`${basePath}/${list.path}?${query}`}>
          View related {list.plural}
        </Link>
      </Button>
    );
  }

  return (
    <Button variant="ghost">
      <Link href={`${basePath}/${list.path}/${value.value?.id}`}>
        View {list.singular} details
      </Link>
    </Button>
  );
}

function ProductImagesCards({ value, onChange, field, foreignList, localList }: {
  value: any;
  onChange: (value: any) => void;
  field: any;
  foreignList: any;
  localList: any;
}) {
  // Mock data for now - in a real implementation, you'd fetch the actual ProductImage data
  const mockProductImages = [
    {
      id: "1",
      image: {
        url: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop",
        filename: "product-1.jpg",
        filesize: 245760,
        width: 300,
        height: 300,
      },
      altText: "Product Image 1",
      imagePath: "/uploads/product-1.jpg",
    },
    {
      id: "2",
      image: {
        url: "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=300&h=300&fit=crop",
        filename: "product-2.jpg",
        filesize: 189440,
        width: 300,
        height: 300,
      },
      altText: "Product Image 2",
      imagePath: "/uploads/product-2.jpg",
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Badge variant="secondary">
            {mockProductImages.length} {mockProductImages.length === 1 ? 'Image' : 'Images'}
          </Badge>
        </div>
        <Button size="sm" variant="outline">
          <Plus className="h-4 w-4 mr-2" />
          Add Image
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {mockProductImages.map((productImage) => (
          <Card key={productImage.id} className="group relative overflow-hidden">
            <CardContent className="p-0">
              <div className="aspect-square relative">
                <Image
                  src={productImage.image.url}
                  alt={productImage.altText || 'Product image'}
                  className="object-cover"
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button size="icon" variant="secondary">
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="secondary">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="destructive">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="p-3">
                <p className="text-sm font-medium truncate">{productImage.altText || 'Untitled'}</p>
                <p className="text-xs text-muted-foreground truncate">{productImage.image.filename}</p>
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-muted-foreground">
                    {productImage.image.width} × {productImage.image.height}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {Math.round(productImage.image.filesize / 1024)}KB
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {mockProductImages.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <div className="mb-4">
            <Plus className="h-12 w-12 mx-auto opacity-50" />
          </div>
          <p className="text-sm">No images added yet</p>
          <Button size="sm" variant="outline" className="mt-2">
            <Plus className="h-4 w-4 mr-2" />
            Add your first image
          </Button>
        </div>
      )}
    </div>
  );
}

export const Field = ({
  field,
  autoFocus,
  value,
  onChange,
  forceValidation,
}: {
  field: any;
  autoFocus?: boolean;
  value: any;
  onChange: (value: any) => void;
  forceValidation?: boolean;
}) => {
  const { list: foreignList } = useList(field.refListKey) || { list: undefined };
  const { list: localList } = useList(field.listKey) || { list: undefined };
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  if (!foreignList || !localList) {
    return <div>Loading...</div>;
  }

  // Handle cards view (more sophisticated relationship management)
  if (value.kind === "cards-view") {
    return (
      <FieldContainer>
        <FieldLabel>{field.label}</FieldLabel>
        {field.description && (
          <FieldDescription>{field.description}</FieldDescription>
        )}
        <ProductImagesCards
          value={value}
          onChange={onChange}
          field={field}
          foreignList={foreignList}
          localList={localList}
        />
      </FieldContainer>
    );
  }

  // Handle count view
  if (value.kind === "count") {
    return (
      <FieldContainer>
        <FieldLabel>{field.label}</FieldLabel>
        {field.description && (
          <FieldDescription>{field.description}</FieldDescription>
        )}
        <div>
          {value.count === 1
            ? `There is 1 ${foreignList.singular} `
            : `There are ${value.count} ${foreignList.plural} `}
          linked to this {localList.singular}
        </div>
      </FieldContainer>
    );
  }

  // Handle standard select view (many or one)
  return (
    <FieldContainer>
      <FieldLabel>{field.label}</FieldLabel>
      {field.description && (
        <FieldDescription>{field.description}</FieldDescription>
      )}
      <Fragment>
        <div className="space-y-2">
          <RelationshipSelect
            controlShouldRenderValue
            autoFocus={autoFocus}
            labelField={field.refLabelField || "name"}
            searchFields={field.refSearchFields || ["name"]}
            list={foreignList}
            state={
              value.kind === "many"
                ? {
                    kind: "many",
                    value: value.value,
                    onChange(newItems: any[]) {
                      onChange?.({
                        ...value,
                        value: newItems,
                      });
                    },
                  }
                : {
                    kind: "one",
                    value: value.value,
                    onChange(newVal: any) {
                      if (value.kind === "one") {
                        onChange?.({
                          ...value,
                          value: newVal,
                        });
                      }
                    },
                  }
            }
          />
          {!field.hideButtons && (
            <div className="flex gap-1 flex-wrap">
              {!!(value.kind === "many"
                ? value.value.length
                : value.kind === "one" && value.value) && (
                <LinkToRelatedItems
                  itemId={value.id}
                  refFieldKey={field.refFieldKey}
                  list={foreignList}
                  value={value}
                />
              )}
            </div>
          )}
        </div>
      </Fragment>
    </FieldContainer>
  );
};

export const Cell = ({ field, item }: { field: any; item: any }) => {
  const { list } = useList(field.fieldMeta.refListKey) || { list: undefined };

  if (field.display === "count") {
    const count = item[`${field.path}Count`] ?? 0;
    return (
      <CellContainer>
        {count} {count === 1 ? list?.singular || 'item' : list?.plural || 'items'}
      </CellContainer>
    );
  }

  const data = item[field.path];
  const items = (Array.isArray(data) ? data : [data]).filter((item) => item);
  const displayItems = items.length < 5 ? items : items.slice(0, 3);
  const overflow = items.length < 5 ? 0 : items.length - 3;

  return (
    <CellContainer>
      {displayItems.map((item: any, index: number) => (
        <Fragment key={item.id}>
          {!!index ? ", " : ""}
          <Link href={`${basePath}/${list?.path || ''}/${item.id}`}>
            {item.label || item.id}
          </Link>
        </Fragment>
      ))}
      <span className="opacity-50 font-medium">
        {overflow ? `, and ${overflow} more` : null}
      </span>
    </CellContainer>
  );
};

export const CardValue = ({
  field,
  item,
  list,
}: {
  field: any;
  item: any;
  list: any;
}) => {
  const data = item[field.path];
  return (
    <FieldContainer>
      <FieldLabel>{field.label}</FieldLabel>
      {(Array.isArray(data) ? data : [data])
        .filter((item) => item)
        .map((item: any, index: number) => (
          <Fragment key={item.id}>
            {!!index ? ", " : ""}
            {item.label || item.id}
          </Fragment>
        ))}
    </FieldContainer>
  );
};

function getForeignIds(value: any) {
  if (typeof value === "string" && value.length > 0) {
    return value.split(",");
  }
  return [];
}

// Modified version of useRelationshipFilterValues that can accept prefetched data
function useRelationshipFilterValues({
  value,
  list,
  prefetchedValues,
}: {
  value: any;
  list: any;
  prefetchedValues?: any[];
}) {
  const foreignIds = getForeignIds(value);
  const where = { id: { in: foreignIds } };

  // If prefetched values are provided, use them
  if (prefetchedValues) {
    return {
      filterValues: prefetchedValues,
      loading: false,
    };
  }

  // Otherwise use SWR to fetch the data
  const { data, error } = useSWR(
    foreignIds.length && list
      ? [`relationship-filter-${list.key}`, foreignIds.join(",")] // Ensure key is stable string
      : null,
    async () => {
      // Use a safe way to access list query name
      const listQueryName =
        list.gqlNames?.listQueryName || `${list.key.toLowerCase()}s`;

      // Use a safe way to access where input name
      const whereInputName =
        list.gqlNames?.whereInputName || `${list.key}WhereInput`;

      // Use the labelField if available, otherwise default to 'name'
      const labelField = list.labelField || "name";

      const result = await getRelationshipOptions(
        list.key,
        where,
        foreignIds.length,
        0,
        labelField,
        "", // No extra selection needed here for filter values
        {
          whereInputName,
          listQueryName,
          listQueryCountName:
            list.gqlNames?.listQueryCountName || `${listQueryName}Count`,
        }
      );

      if (result.success) {
        // Return the items array directly on success, default to empty array
        return result.data?.items ?? [];
      } else {
        console.error("Error fetching relationship filter values:", result.error);
        // Throw error for SWR to catch
        throw new Error(result.error || "Failed to fetch relationship filter values");
      }
    }
  );

  const loading = !data && !error;
  const labelField = list.labelField || "name";

  // Process the data returned by SWR (which is the items array on success)
  const resolvedFilterValues = data
    ? data.map((item: any) => ({
        id: item.id,
        label: item[labelField] || item.id,
      }))
    : foreignIds.map((f: string) => ({ label: f, id: f })); // Fallback if data is not yet loaded or error occurred

  return {
    filterValues: resolvedFilterValues,
    loading,
  };
}

// Standalone Filter component that can be used from server components
export const Filter = ({
  onChange,
  value,
  filterValues,
  list,
  refLabelField,
  refSearchFields,
}: {
  onChange: any;
  value: any;
  filterValues?: any[];
  list: any;
  refLabelField: string;
  refSearchFields: string[];
}) => {
  const { filterValues: resolvedFilterValues, loading } =
    useRelationshipFilterValues({
      value,
      list,
      prefetchedValues: filterValues,
    });
  const state = {
    kind: "many" as const,
    value: resolvedFilterValues,
    onChange(newItems: any[]) {
      onChange(newItems.map((item: any) => item.id).join(","));
    },
  };
  // Add the missing props required by RelationshipSelect
  return (
    <RelationshipSelect
      controlShouldRenderValue
      list={list}
      labelField={refLabelField}
      searchFields={refSearchFields}
      state={state}
    />
  );
};

export const controller = (config: {
  path: string;
  label: string;
  description?: string;
  listKey: string;
  fieldMeta: {
    refListKey: string;
    refLabelField?: string;
    refSearchFields?: string[];
    refFieldKey?: string;
    many?: boolean;
    displayMode?: "cards" | "count";
    cardFields?: string[];
    inlineCreate?: boolean;
    inlineEdit?: boolean;
    linkToItem?: boolean;
    removeMode?: "disconnect" | "none";
    inlineConnect?: boolean;
    hideCreate?: boolean;
    hideButtons?: boolean;
  };
}) => {
  const cardsDisplayOptions =
    config.fieldMeta.displayMode === "cards"
      ? {
          cardFields: config.fieldMeta.cardFields,
          inlineCreate: config.fieldMeta.inlineCreate,
          inlineEdit: config.fieldMeta.inlineEdit,
          linkToItem: config.fieldMeta.linkToItem,
          removeMode: config.fieldMeta.removeMode,
          inlineConnect: config.fieldMeta.inlineConnect,
        }
      : undefined;

  const refLabelField = config.fieldMeta.refLabelField;
  const refSearchFields = config.fieldMeta.refSearchFields;

  return {
    refFieldKey: config.fieldMeta.refFieldKey,
    many: config.fieldMeta.many,
    listKey: config.listKey,
    path: config.path,
    label: config.label,
    description: config.description,
    display:
      config.fieldMeta.displayMode === "count" ? "count" : "cards-or-select",
    refLabelField,
    refSearchFields,
    refListKey: config.fieldMeta.refListKey,
    graphqlSelection:
      config.fieldMeta.displayMode === "count"
        ? `${config.path}Count`
        : `${config.path} {
              id
              label: ${refLabelField}
            }`,
    hideCreate: config.fieldMeta.hideCreate,
    hideButtons: config.fieldMeta.hideButtons,
    defaultValue:
      cardsDisplayOptions !== undefined
        ? {
            kind: "cards-view",
            currentIds: new Set(),
            id: null,
            initialIds: new Set(),
            itemBeingCreated: false,
            itemsBeingEdited: new Set(),
            displayOptions: cardsDisplayOptions,
          }
        : config.fieldMeta.many
        ? {
            id: null,
            kind: "many",
            initialValue: [],
            value: [],
          }
        : { id: null, kind: "one", value: null, initialValue: null },
    deserialize: (data: any) => {
      console.log("Relationship deserealize input:", data);

      if (config.fieldMeta.displayMode === "count") {
        return {
          id: data.id,
          kind: "count",
          count: data[`${config.path}Count`] ?? 0,
        };
      }
      if (cardsDisplayOptions !== undefined) {
        const initialIds = new Set(
          (Array.isArray(data[config.path])
            ? data[config.path]
            : data[config.path]
            ? [data[config.path]]
            : []
          ).map((x: any) => x.id)
        );
        return {
          kind: "cards-view",
          id: data.id,
          itemsBeingEdited: new Set(),
          itemBeingCreated: false,
          initialIds,
          currentIds: initialIds,
          displayOptions: cardsDisplayOptions,
        };
      }
      if (config.fieldMeta.many) {
        let value = (data[config.path] || []).map((x: any) => ({
          id: x.id,
          label: x.label || x.id,
        }));
        return {
          kind: "many",
          id: data.id,
          initialValue: value,
          value,
        };
      }
      let value = data[config.path];
      if (value) {
        value = {
          id: value.id,
          label: value.label || value.id,
        };
      }
      return {
        kind: "one",
        id: data.id,
        value,
        initialValue: value,
      };
    },
    validate(value: any) {
      return (
        value.kind !== "cards-view" ||
        (value.itemsBeingEdited.size === 0 && !value.itemBeingCreated)
      );
    },
    serialize: (state: any) => {
      console.log("Relationship serialize input:", state);

      if (state.kind === "many") {
        const newAllIds = new Set(state.value.map((x: any) => x.id));
        const initialIds = new Set(state.initialValue.map((x: any) => x.id));
        let disconnect = state.initialValue
          .filter((x: any) => !newAllIds.has(x.id))
          .map((x: any) => ({ id: x.id }));
        let connect = state.value
          .filter((x: any) => !initialIds.has(x.id))
          .map((x: any) => ({ id: x.id }));
        if (disconnect.length || connect.length) {
          let output: Record<string, any> = {};

          if (disconnect.length) {
            output.disconnect = disconnect;
          }

          if (connect.length) {
            output.connect = connect;
          }

          return {
            [config.path]: output,
          };
        }
      } else if (state.kind === "one") {
        if (state.initialValue && !state.value) {
          return { [config.path]: { disconnect: true } };
        } else if (state.value && state.value.id !== state.initialValue?.id) {
          return {
            [config.path]: {
              connect: {
                id: state.value.id,
              },
            },
          };
        }
      } else if (state.kind === "cards-view") {
        const currentIdsSet = new Set(state.currentIds);
        const initialIdsSet = new Set(state.initialIds);
        let disconnect = [...state.initialIds]
          .filter((id: string) => !currentIdsSet.has(id))
          .map((id: string) => ({ id }));
        let connect = [...state.currentIds]
          .filter((id: string) => !initialIdsSet.has(id))
          .map((id: string) => ({ id }));

        if (config.fieldMeta.many) {
          if (disconnect.length || connect.length) {
            return {
              [config.path]: {
                connect: connect.length ? connect : undefined,
                disconnect: disconnect.length ? disconnect : undefined,
              },
            };
          }
        } else if (connect.length) {
          return {
            [config.path]: {
              connect: connect[0],
            },
          };
        } else if (disconnect.length) {
          return { [config.path]: { disconnect: true } };
        }
      }
      return {};
    },
  };
};
