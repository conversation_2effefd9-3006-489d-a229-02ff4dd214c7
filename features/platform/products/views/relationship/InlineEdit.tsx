"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Pencil, Trash2, ExternalLink } from "lucide-react";
import Image from "next/image";
import { basePath } from "@/features/dashboard/lib/config";
import Link from "next/link";

export function InlineEdit({
  list,
  fields,
  onSave,
  selectedFields,
  itemGetter,
  onCancel,
}: {
  list: any;
  fields: string[];
  onSave: (itemGetter: any) => void;
  selectedFields: string;
  itemGetter: any;
  onCancel: () => void;
}) {
  const [isEditing, setIsEditing] = useState(false);

  // Get the item data
  const item = itemGetter.data;
  
  // For ProductImage items, we want to show the image field specially
  const isProductImage = list.key === "ProductImage";
  
  if (isProductImage && item) {
    const imageData = itemGetter.get("image");
    const altTextData = itemGetter.get("altText");
    const imagePathData = itemGetter.get("imagePath");
    
    return (
      <Card className="group relative overflow-hidden">
        <CardContent className="p-0">
          <div className="aspect-square relative">
            {imageData.data?.url ? (
              <Image
                src={imageData.data.url}
                alt={altTextData.data || 'Product image'}
                className="object-cover"
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : imagePathData.data ? (
              <Image
                src={imagePathData.data}
                alt={altTextData.data || 'Product image'}
                className="object-cover"
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <span className="text-muted-foreground">No image</span>
              </div>
            )}
            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
              <Button size="icon" variant="secondary">
                <Pencil className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="secondary" asChild>
                <Link href={`${basePath}/${list.path}/${item.id}`}>
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </Button>
              <Button size="icon" variant="destructive">
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="p-3">
            <p className="text-sm font-medium truncate">{altTextData.data || 'Untitled'}</p>
            <p className="text-xs text-muted-foreground truncate">
              {imageData.data?.filename || imagePathData.data || 'No filename'}
            </p>
            {imageData.data && (
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-muted-foreground">
                  {imageData.data.width} × {imageData.data.height}
                </span>
                <span className="text-xs text-muted-foreground">
                  {Math.round((imageData.data.filesize || 0) / 1024)}KB
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Fallback for other types
  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-2">
          {fields.map((fieldPath) => {
            const fieldData = itemGetter.get(fieldPath);
            const field = list.fields[fieldPath];
            
            return (
              <div key={fieldPath}>
                <label className="text-sm font-medium">{field?.label || fieldPath}</label>
                <div className="text-sm text-muted-foreground">
                  {fieldData.data?.toString() || 'N/A'}
                </div>
              </div>
            );
          })}
          
          <div className="flex gap-2 mt-4">
            <Button size="sm" variant="outline">
              Edit
            </Button>
            <Button size="sm" variant="destructive">
              Remove
            </Button>
            <Button size="sm" variant="ghost" asChild>
              <Link href={`${basePath}/${list.path}/${item.id}`}>
                View Details
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
