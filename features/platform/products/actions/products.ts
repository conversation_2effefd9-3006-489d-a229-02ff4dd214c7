'use server';

import { revalidatePath } from 'next/cache';
import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

// Interface for product data
interface Product {
  id: string;
  title: string;
  handle: string;
  status: string;
  thumbnail?: string;
  [key: string]: unknown;
}

/**
 * Get list of products
 */
export async function getProducts(
  where: Record<string, unknown> = {},
  take: number = 10,
  skip: number = 0,
  orderBy: Array<Record<string, string>> = [{ createdAt: 'desc' }],
  selectedFields: string = `
    id
    title
    handle
    status
    subtitle
    thumbnail
    discountable
    isGiftcard
    createdAt
    updatedAt
    productType {
      id
      value
    }
    productVariants {
      id
      title
      sku
      inventoryQuantity
      manageInventory
    }
    productImages {
      id
      image {
        url
      }
      imagePath
      altText
    }
    productCategories {
      id
      title
    }
    productCollections {
      id
      title
    }
  `
) {
  const query = `
    query GetProducts($where: ProductWhereInput, $take: Int!, $skip: Int!, $orderBy: [ProductOrderByInput!]) {
      items: products(where: $where, take: $take, skip: $skip, orderBy: $orderBy) {
        ${selectedFields}
      }
      count: productsCount(where: $where)
    }
  `;

  const response = await keystoneClient(query, { where, take, skip, orderBy });
  return response;
}

/**
 * Get a single product by ID with full details
 */
export async function getProduct(productId: string) {
  const query = `
    query ($id: ID!) {
      product(where: { id: $id }) {
        id
        title
        handle
        subtitle
        status
        thumbnail
        description {
          document
        }
        isGiftcard
        discountable
        metadata
        externalId
        createdAt
        updatedAt
        dimensionsRange
        defaultDimensions
        productType {
          id
          value
        }
        shippingProfile {
          id
          name
        }
        productCollections {
          id
          title
          handle
        }
        productCategories {
          id
          title
          handle
        }
        productTags {
          id
          value
        }
        productImages {
          id
          image {
            url
          }
          imagePath
          altText
        }
        productOptions {
          id
          title
          productOptionValues {
            id
            value
            metadata
          }
        }
        productVariants {
          id
          title
          sku
          barcode
          ean
          upc
          variantRank
          inventoryQuantity
          allowBackorder
          manageInventory
          hsCode
          originCountry
          midCode
          material
          metadata
          productOptionValues {
            id
            value
            productOption {
              id
              title
            }
          }
          prices {
            id
            amount
            compareAmount
            minQuantity
            maxQuantity
            currency {
              code
              symbol
              symbolNative
            }
            region {
              id
              code
              name
              currency {
                code
                symbol
                symbolNative
              }
            }
            priceSet {
              id
            }
          }
          measurements {
            id
            value
            unit
            type
          }
        }
      }
    }
  `;

  const cacheOptions = {
    next: {
      tags: [`product-${productId}`],
      revalidate: 3600, // Cache for 1 hour
    },
  };

  const response = await keystoneClient(query, { id: productId }, cacheOptions);
  return response;
}

/**
 * Update product
 */
export async function updateProduct(id: string, data: Record<string, any>) {
  const query = `
    mutation UpdateProduct($id: ID!, $data: ProductUpdateInput!) {
      updateProduct(where: { id: $id }, data: $data) {
        id
        title
        handle
        subtitle
        status
        description {
          document
        }
        isGiftcard
        discountable
        metadata
        externalId
      }
    }
  `;

  const response = await keystoneClient(query, { id, data });

  if (response.success) {
    revalidatePath(`/dashboard/platform/products/${id}`);
    revalidatePath(`/dashboard/platform/products`);
  } else {
    console.error(`Failed to update product ${id}:`, response.error);
  }

  return response;
}

/**
 * Update product status
 */
export async function updateProductStatus(id: string, status: string) {
  return updateProduct(id, { status });
}

/**
 * Get filtered products based on status and search parameters
 */
export async function getFilteredProducts(
  status: string | null = null,
  search: string | null = null,
  page: number = 1,
  pageSize: number = 10,
  sort: { field: string; direction: 'ASC' | 'DESC' } | null = null
) {
  const where: Record<string, unknown> = {};

  // Add status filter if provided and not 'all'
  if (status && status !== 'all') {
    where.status = { equals: status };
  }

  // Add search filter if provided
  if (search) {
    where.OR = [
      { title: { contains: search, mode: 'insensitive' } },
      { handle: { contains: search, mode: 'insensitive' } },
      { productVariants: { some: { sku: { contains: search, mode: 'insensitive' } } } },
    ];
  }

  // Calculate pagination
  const skip = (page - 1) * pageSize;

  // Handle sorting
  const orderBy = sort
    ? [{ [sort.field]: sort.direction.toLowerCase() }]
    : [{ createdAt: 'desc' }];

  return getProducts(where, pageSize, skip, orderBy);
}

/**
 * Get product counts by status
 */
export async function getProductStatusCounts() {
  const query = `
    query GetProductStatusCounts {
      draft: productsCount(where: { status: { equals: draft } })
      proposed: productsCount(where: { status: { equals: proposed } })
      published: productsCount(where: { status: { equals: published } })
      rejected: productsCount(where: { status: { equals: rejected } })
      all: productsCount
    }
  `;

  const response = await keystoneClient(query);
  return response;
}

/**
 * Get regions for pricing
 */
export async function getRegions() {
  const query = `
    query GetRegions {
      regions {
        id
        code
        name
        taxRate
        currency {
          code
          symbol
          symbolNative
          name
        }
      }
    }
  `;

  const response = await keystoneClient(query);
  return response;
}

/**
 * Create product option
 */
export async function createProductOption(data: { title: string; productId: string }) {
  const query = `
    mutation CreateProductOption($data: ProductOptionCreateInput!) {
      createProductOption(data: $data) {
        id
        title
        productOptionValues {
          id
          value
        }
      }
    }
  `;

  const response = await keystoneClient(query, {
    data: {
      title: data.title,
      product: {
        connect: { id: data.productId },
      },
    },
  });

  if (response.success) {
    revalidatePath(`/dashboard/platform/products/${data.productId}`);
  }

  return response;
}

/**
 * Update product option
 */
export async function updateProductOption(id: string, data: { title: string }) {
  const query = `
    mutation UpdateProductOption($id: ID!, $data: ProductOptionUpdateInput!) {
      updateProductOption(where: { id: $id }, data: $data) {
        id
        title
        productOptionValues {
          id
          value
        }
      }
    }
  `;

  const response = await keystoneClient(query, { id, data });

  if (response.success) {
    // Get product ID from the option to revalidate the correct path
    const productQuery = `
      query GetProductFromOption($id: ID!) {
        productOption(where: { id: $id }) {
          product {
            id
          }
        }
      }
    `;

    const productResponse = await keystoneClient(productQuery, { id });
    if (productResponse.success && productResponse.data.productOption?.product?.id) {
      revalidatePath(`/dashboard/platform/products/${productResponse.data.productOption.product.id}`);
    }
  }

  return response;
}

/**
 * Create product option value
 */
export async function createProductOptionValue(data: { value: string; optionId: string }) {
  const query = `
    mutation CreateProductOptionValue($data: ProductOptionValueCreateInput!) {
      createProductOptionValue(data: $data) {
        id
        value
        productOption {
          id
          title
          product {
            id
          }
        }
      }
    }
  `;

  const response = await keystoneClient(query, {
    data: {
      value: data.value,
      productOption: {
        connect: { id: data.optionId },
      },
    },
  });

  if (response.success && response.data.createProductOptionValue?.productOption?.product?.id) {
    revalidatePath(`/dashboard/platform/products/${response.data.createProductOptionValue.productOption.product.id}`);
  }

  return response;
}

/**
 * Update product option value
 */
export async function updateProductOptionValue(id: string, data: { value: string }) {
  const query = `
    mutation UpdateProductOptionValue($id: ID!, $data: ProductOptionValueUpdateInput!) {
      updateProductOptionValue(where: { id: $id }, data: $data) {
        id
        value
        productOption {
          id
          title
          product {
            id
          }
        }
      }
    }
  `;

  const response = await keystoneClient(query, { id, data });

  if (response.success && response.data.updateProductOptionValue?.productOption?.product?.id) {
    revalidatePath(`/dashboard/platform/products/${response.data.updateProductOptionValue.productOption.product.id}`);
  }

  return response;
}

/**
 * Delete product option value
 */
export async function deleteProductOptionValue(id: string) {
  // First get the product ID for revalidation
  const productQuery = `
    query GetProductFromOptionValue($id: ID!) {
      productOptionValue(where: { id: $id }) {
        productOption {
          product {
            id
          }
        }
      }
    }
  `;

  const productResponse = await keystoneClient(productQuery, { id });
  const productId = productResponse.success ? productResponse.data.productOptionValue?.productOption?.product?.id : null;

  const query = `
    mutation DeleteProductOptionValue($id: ID!) {
      deleteProductOptionValue(where: { id: $id }) {
        id
      }
    }
  `;

  const response = await keystoneClient(query, { id });

  if (response.success && productId) {
    revalidatePath(`/dashboard/platform/products/${productId}`);
  }

  return response;
}